package com.kerryprops.kip.audit;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * Utility class for JSON serialization and deserialization operations.
 *
 * <p>
 * Provides methods for converting Java objects to JSON strings and vice
 * versa using a pre-configured Jackson ObjectMapper instance.
 *
 * <AUTHOR> Zhang
 * @since 1.0
 */
@Slf4j
@Getter
@RequiredArgsConstructor
public enum JsonUtils {

    /**
     * Main Jackson ObjectMapper instance with custom configuration.
     *
     * <p>
     * Supports Java 8 date/time types and custom serialization settings.
     */
    JACKSON(createObjectMapper());

    private final ObjectMapper objectMapper;

    /**
     * Converts the object to its JSON string representation.
     *
     * @param o the object to convert
     * @return JSON string representation of the object
     */
    public static String toJson(Object o) {
        return JACKSON.objToStr(o);
    }

    /**
     * Converts a JSON string to an object of the specified class.
     *
     * @param str   the JSON string to convert
     * @param clazz target class type
     * @param <T>   type of the target object
     * @return Optional containing the deserialized object, empty if conversion fails
     */
    public static <T> Optional<T> fromJson(String str, Class<T> clazz) {
        return JACKSON.strToObj(str, clazz);
    }

    private static ObjectMapper createObjectMapper() {
        return new Jackson2ObjectMapperBuilder().serializationInclusion(JsonInclude.Include.NON_NULL)
                .failOnUnknownProperties(false)
                .failOnEmptyBeans(false)
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(
                        AppConstants.LOCAL_DATE_TIME_FORMATTER))
                .serializerByType(LocalDate.class, new LocalDateSerializer(
                        DateTimeFormatter.ISO_LOCAL_DATE))
                .deserializerByType(LocalDate.class, new LocalDateDeserializer(
                        DateTimeFormatter.ISO_LOCAL_DATE))
                .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(
                        AppConstants.LOCAL_DATE_TIME_FORMATTER))
                .serializerByType(LocalTime.class, new LocalTimeSerializer(
                        DateTimeFormatter.ISO_LOCAL_TIME))
                .deserializerByType(LocalTime.class, new LocalTimeDeserializer(
                        DateTimeFormatter.ISO_LOCAL_TIME))
                .build();
    }

    /**
     * Converts the object to its JSON string representation.
     *
     * @param object the object to convert
     * @return JSON string representation of the object
     */
    private String objToStr(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Error occurred while converting object to JSON string", e);
            return "";
        }
    }

    /**
     * Converts a JSON string to an object using TypeReference.
     *
     * @param str           the JSON string to convert
     * @param typeReference TypeReference describing the target type
     * @param <T>           type of the target object
     * @return Optional containing the deserialized object, empty if conversion fails
     */
    private <T> Optional<T> strToObj(String str, TypeReference<T> typeReference) {
        if (str.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(objectMapper.readValue(str, typeReference));
        } catch (JsonProcessingException e) {
            log.error("Error occurred while reading JSON string '{}' into type-reference {}", str, typeReference, e);
            return Optional.empty();
        }
    }

    /**
     * Converts a JSON string to an object of the specified class.
     *
     * @param str   the JSON string to convert
     * @param clazz target class
     * @param <T>   type of the target object
     * @return Optional containing the deserialized object, empty if conversion fails
     */
    private <T> Optional<T> strToObj(String str, Class<T> clazz) {
        Assert.notNull(clazz, "Class type must not be null");
        Assert.notNull(str, "JSON string must not be null");
        if (str.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(objectMapper.readValue(str, clazz));
        } catch (JsonProcessingException e) {
            log.error("Error occurred while reading JSON string '{}' into type {}", str, clazz, e);
            return Optional.empty();
        }
    }
}
