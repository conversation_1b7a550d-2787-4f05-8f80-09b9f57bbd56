package com.kerryprops.kip.audit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Optional;

/**
 * Thread-local context for storing audit information during request processing.
 * This class manages user context extracted from HTTP headers and provides
 * it to the audit system for tracking changes.
 *
 * <AUTHOR>
 */
@Slf4j
public class AuditContext {

    private static final ThreadLocal<XuserInfo> USER_CONTEXT = new ThreadLocal<>();

    private static final ThreadLocal<AuditRequestInfo> AUDIT_INFO_STORAGE = new ThreadLocal<>();

    public static XuserInfo getCurrentUser() {
        return Optional.ofNullable(USER_CONTEXT.get()).orElse(XuserInfo.ANONYMOUS_USER);
    }

    public static void storeCurrentUser(XuserInfo userInfo) {
        Assert.notNull(userInfo, "User information cannot be null");
        USER_CONTEXT.set(userInfo);
        log.info("Set Audit user information for user name: {}", userInfo.getNickName());
    }

    public static String getCurrentUserNickname() {
        return getCurrentUser().getNickName();
    }

    public static Long getCurrentUserId() {
        return getCurrentUser().getUserId();
    }

    public static void storeAuditRequestInfo(AuditRequestInfo auditRequestInfo) {
        Assert.notNull(auditRequestInfo, "AuditRequestInfo cannot be null");
        AUDIT_INFO_STORAGE.set(auditRequestInfo);
    }

    public static AuditRequestInfo getAuditRequestInfo() {
        return AUDIT_INFO_STORAGE.get();
    }


    public static void clear() {
        USER_CONTEXT.remove();
        AUDIT_INFO_STORAGE.remove();
        log.info("Cleared audit context for current thread");
    }

}
