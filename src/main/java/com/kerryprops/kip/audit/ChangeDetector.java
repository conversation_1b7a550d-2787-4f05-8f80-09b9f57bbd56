package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.ValueChange;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class ChangeDetector {

    private final FieldAnalyzer fieldAnalyzer;
    
    /**
     * JaVers 实例，用于实体比较和变更检测
     */
    private final Javers javers = JaversBuilder.javers().build();

    /**
     * 使用 JaVers 检测实体变更
     * 支持深度对象比较、嵌套对象和集合的变更检测
     */
    public List<AuditEventRequest.FieldChange> detectChanges(@Nullable Object oldEntity,
                                                           @Nullable Object newEntity) {
        validateEntities(oldEntity, newEntity);
        
        var diff = javers.compare(oldEntity, newEntity);
        if (!diff.hasChanges()) {
            log.info("No changes detected, oldEntity: {}, newEntity: {}", oldEntity, newEntity);
            return new ArrayList<>();
        }

        return processChanges(diff.getChanges(), oldEntity, newEntity);
    }

    /**
     * 验证输入实体参数
     */
    private void validateEntities(@Nullable Object oldEntity, @Nullable Object newEntity) {
        if (oldEntity == null && newEntity == null) {
            log.warn("Both oldEntity and newEntity are null, returning empty changes");
            throw new IllegalArgumentException("Both oldEntity and newEntity cannot be null");
        }
    }

    /**
     * 处理变更列表，过滤并转换为审计字段变更
     */
    private List<AuditEventRequest.FieldChange> processChanges(List<?> changes, 
                                                             @Nullable Object oldEntity, 
                                                             @Nullable Object newEntity) {
        Class<?> entityClass = determineEntityClass(oldEntity, newEntity);
        
        return changes.stream()
                .filter(PropertyChange.class::isInstance)
                .map(PropertyChange.class::cast)
                .filter(change -> !fieldAnalyzer.shouldSkipAuditField(entityClass, change.getPropertyName()))
                .map(change -> createFieldChange(change, entityClass))
                .toList();
    }

    /**
     * 确定实体类型
     */
    private Class<?> determineEntityClass(@Nullable Object oldEntity, @Nullable Object newEntity) {
        if (oldEntity != null) {
            return oldEntity.getClass();
        }
        if (newEntity != null) {
            return newEntity.getClass();
        }
        throw new IllegalStateException("Both entities cannot be null");
    }

    /**
     * 创建字段变更对象
     */
    private AuditEventRequest.FieldChange createFieldChange(PropertyChange<?> propertyChange, Class<?> entityClass) {
        var auditChange = new AuditEventRequest.FieldChange();
        String fieldName = propertyChange.getPropertyName();
        
        auditChange.setFieldName(fieldName);
        auditChange.setFieldDisplayName(fieldAnalyzer.resolveFieldDisplayName(entityClass, fieldName));
        
        setFieldValues(auditChange, propertyChange);
        return auditChange;
    }

    /**
     * 设置字段值和类型
     */
    private void setFieldValues(AuditEventRequest.FieldChange auditChange, PropertyChange<?> propertyChange) {
        if (propertyChange instanceof ValueChange valueChange) {
            setValueChangeFields(auditChange, valueChange);
        } else {
            setObjectChangeFields(auditChange, propertyChange);
        }
    }

    /**
     * 设置值变更字段
     */
    private void setValueChangeFields(AuditEventRequest.FieldChange auditChange, ValueChange valueChange) {
        Object oldValue = valueChange.getLeft();
        Object newValue = valueChange.getRight();
        
        auditChange.setFieldType(getFieldType(oldValue, newValue));
        auditChange.setOldValue(oldValue != null ? oldValue.toString() : null);
        auditChange.setNewValue(newValue != null ? newValue.toString() : null);
    }

    /**
     * 设置对象变更字段
     */
    private void setObjectChangeFields(AuditEventRequest.FieldChange auditChange, PropertyChange<?> propertyChange) {
        auditChange.setFieldType("OBJECT");
        auditChange.setOldValue(propertyChange.getLeft() != null ? propertyChange.getLeft().toString() : "");
        auditChange.setNewValue(propertyChange.getRight() != null ? propertyChange.getRight().toString() : "");
    }

    private String getFieldType(@Nullable Object oldValue, @Nullable Object newValue) {
        Assert.state(oldValue != null || newValue != null,
                "Both oldValue and newValue cannot be null");
        return oldValue != null ? oldValue.getClass().getSimpleName()
                : newValue.getClass().getSimpleName();
    }
}