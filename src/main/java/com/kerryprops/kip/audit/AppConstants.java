package com.kerryprops.kip.audit;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 服务应用程序的集中常量.
 *
 * <p>
 * 该类包含整个应用程序中使用的各种常量，包括日期/时间格式、关联 ID 键和应用程序特定的前缀.
 *
 * <p>
 * 此类中定义的常量用于在整个应用程序中保持一致性和可读性.它们用于各种组件，例如日志记录、数据格式化和标识符生成.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@UtilityClass
public final class AppConstants {

    /**
     * 用于格式化和解析本地日期/时间的日期-时间模式.
     *
     * <p>
     * 模式： "yyyy-MM-dd HH:mm:ss"
     *
     * <p>
     * 此模式用于格式化和解析应用程序中的日期/时间值.它与 {@link #LOCAL_DATE_TIME_FORMATTER} 一起使用，以确保整个应用程序中日期/时间格式的一致性.
     */
    public static final String LOCAL_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 配置为使用 LOCAL_DATE_TIME_PATTERN 的 DateTimeFormatter 实例，并设置为 Asia/Shanghai 时区.
     *
     * <p>
     * 此格式化程序用于格式化和解析应用程序中的日期/时间值.它配置为使用 {@link #LOCAL_DATE_TIME_PATTERN} 并设置为 Asia/Shanghai 时区，以确保整个应用程序中日期/时间格式的一致性.
     */
    public static final DateTimeFormatter LOCAL_DATE_TIME_FORMATTER
            = DateTimeFormatter.ofPattern(LOCAL_DATE_TIME_PATTERN).withZone(ZoneId.of("Asia/Shanghai"));
    public static final String CACHE_KEY_SEPARATOR = "#";
    /**
     * 应用程序的基本包名.
     */
    public static final String BASE_PACKAGE = "com.kerryprops.kip";
    /**
     * 用户代理常量，用于整个应用程序的一致使用.
     *
     * <p>
     * 此常量用于表示整个应用程序中的用户代理.它用于确保在各种组件中（如日志记录和数据格式化）一致地使用用户代理.
     */
    static final String USER_AGENT = "User-Agent";
}