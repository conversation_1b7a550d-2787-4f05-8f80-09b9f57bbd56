package com.kerryprops.kip.audit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.ArrayList;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class AuditQueue {

    private final BlockingQueue<AuditEventRequest> queue;
    private final AtomicBoolean processing = new AtomicBoolean(false);

    private final AuditClient auditClient;
    private final AuditProperties auditProperties;

    public AuditQueue(AuditClient auditClient, AuditProperties auditProperties) {
        this.auditClient = auditClient;
        this.auditProperties = auditProperties;
        this.queue = new ArrayBlockingQueue<>(auditProperties.getQueue().getCapacity());
    }

    public void enqueue(AuditEventRequest auditLog) {
        if (!queue.offer(auditLog)) {
            log.warn("队列已满，丢弃审计事件，打印到控制台: serviceId={}", auditLog.getServiceId());
            // 队列已满时打印审计记录到控制台
            log.info("AUDIT_FALLBACK: {}", JsonUtils.toJson(auditLog));
        }
    }

    @Scheduled(fixedDelayString = "${audit.fixed-delay:1000}")
    @Async("auditTaskExecutor")
    public void processQueue() {
        // 防止重复执行
        if (!processing.compareAndSet(false, true)) {
            log.warn("上一个审计任务还在执行中，跳过本次执行");
            return;
        }

        try {
            var batch = new ArrayList<AuditEventRequest>();
            queue.drainTo(batch, auditProperties.getQueue().getBatchSize());

            if (batch.isEmpty()) {
                log.trace("No audit logs to process");
                return;
            }

            auditClient.send(batch);
            log.debug("Processed {} audit logs", batch.size());
        } catch (Exception e) {
            log.error("Error processing audit logs: ", e);
        } finally {
            processing.set(false);
        }
    }

}