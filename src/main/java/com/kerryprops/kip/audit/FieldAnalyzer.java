package com.kerryprops.kip.audit;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
public class FieldAnalyzer {

    /**
     * 字段显示名称缓存
     * key: entityClass:fieldName, value: 字段显示名称
     */
    private static final Cache<String, String> FIELD_DISPLAY_NAME_CACHE = Caffeine.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .recordStats()
            .removalListener(
                    (String key, String value, RemovalCause cause) -> 
                            log.debug("字段显示名称缓存移除 - 键: {}, 原因: {}", key, cause))
            .build();

    /**
     * 字段审计决策缓存，避免重复计算
     * key: entityClass:fieldName, value: 是否应该审计该字段
     */
    private static final Cache<String, Boolean> FIELD_AUDIT_DECISION_CACHE = Caffeine.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .recordStats()
            .removalListener(
                    (String key, Boolean value, RemovalCause cause) -> 
                            log.debug("字段审计决策缓存移除 - 键: {}, 原因: {}", key, cause))
            .build();

    private final AuditProperties auditProperties;

    /**
     * 判断字段是否应该被审计
     * 实现多层级的字段过滤逻辑，按优先级顺序处理
     *
     * @param entityClass 实体类
     * @param fieldName   字段名
     * @return 是否应该审计该字段
     */
    public boolean shouldSkipAuditField(Class<?> entityClass, String fieldName) {
        String cacheKey = entityClass.getName() + ":" + fieldName;
        return FIELD_AUDIT_DECISION_CACHE.get(cacheKey, key -> {
            Field field = findFieldInClass(entityClass, fieldName);

            // 1. 字段级 @AuditField.exclude = true (最高优先级，强制排除)
            if (field.isAnnotationPresent(AuditField.class)) {
                AuditField auditField = field.getAnnotation(AuditField.class);
                if (auditField.exclude()) {
                    log.trace("字段 {} 被 @AuditField.exclude 强制排除", fieldName);
                    return true;
                }
            }

            // 2. 全局配置 excludedFields (全局排除列表)
            if (auditProperties.getExcludedFields().contains(fieldName)) {
                log.trace("字段 {} 被全局配置排除", fieldName);
                return true;
            }

            // 3. 类级 @AuditEntity.excludeFields (类级排除列表)
            if (entityClass.isAnnotationPresent(AuditEntity.class)) {
                AuditEntity auditEntity = entityClass.getAnnotation(AuditEntity.class);
                if (Arrays.asList(auditEntity.excludeFields()).contains(fieldName)) {
                    log.trace("字段 {} 被 @AuditEntity.excludeFields 排除", fieldName);
                    return true;
                }
            }

            // 4. 检查字段级别的明确包含设置
            boolean fieldExplicitInclude = false;
            if (field.isAnnotationPresent(AuditField.class)) {
                AuditField auditField = field.getAnnotation(AuditField.class);
                if (auditField.include()) {
                    fieldExplicitInclude = true;
                } else {
                    log.trace("字段 {} 被 @AuditField.include=false 排除", fieldName);
                    return true;
                }
            }

            // 5. 类级 @AuditEntity.includeFields (白名单模式)
            if (entityClass.isAnnotationPresent(AuditEntity.class)) {
                AuditEntity auditEntity = entityClass.getAnnotation(AuditEntity.class);
                if (auditEntity.includeFields().length > 0) {
                    boolean inWhitelist = Arrays.asList(auditEntity.includeFields()).contains(fieldName);
                    if (!inWhitelist) {
                        log.trace("字段 {} 不在 @AuditEntity.includeFields 白名单中", fieldName);
                        return true;
                    }
                }
            }

            // 6. 默认包含策略检查（但字段明确包含的不受此限制）
            if (!fieldExplicitInclude) {
                boolean shouldIncludeByDefault = getDefaultIncludeStrategy(entityClass);
                if (!shouldIncludeByDefault) {
                    log.trace("字段 {} 被默认策略排除", fieldName);
                    return true;
                }
            }

            return false;
        });
    }

    /**
     * 解析字段显示名称
     * 优先级：@AuditField.alias > 全局配置 fieldAliases > 驼峰转换
     *
     * @param entityClass 实体类
     * @param fieldName   字段名
     * @return 字段显示名称
     */
    public String resolveFieldDisplayName(Class<?> entityClass, String fieldName) {
        String cacheKey = entityClass.getName() + ":" + fieldName;
        return FIELD_DISPLAY_NAME_CACHE.get(cacheKey, key -> {
            try {
                // 1. 优先使用 @AuditField.alias 注解别名
                Field field = findFieldInClass(entityClass, fieldName);
                if (field.isAnnotationPresent(AuditField.class)) {
                    AuditField auditField = field.getAnnotation(AuditField.class);
                    if (!auditField.alias().trim().isEmpty()) {
                        log.trace("字段 {} 使用注解别名: {}", fieldName, auditField.alias());
                        return auditField.alias().trim();
                    }
                }

                // 2. 使用全局配置中的字段别名
                String globalAlias = auditProperties.getFieldAliases().get(fieldName);
                if (globalAlias != null && !globalAlias.trim().isEmpty()) {
                    log.debug("字段 {} 使用全局配置别名: {}", fieldName, globalAlias);
                    return globalAlias.trim();
                }

                // 3. 回退到驼峰转换
                String displayName = convertFieldNameToDisplayName(fieldName);
                log.debug("字段 {} 使用驼峰转换别名: {}", fieldName, displayName);
                return displayName;
            } catch (Exception e) {
                log.warn("解析字段 {} 显示名称时发生异常，使用驼峰转换: {}", fieldName, e.getMessage());
                return convertFieldNameToDisplayName(fieldName);
            }
        });
    }

    /**
     * 获取实体的默认包含策略
     * 优先级：@AuditEntity.defaultInclude > 全局配置 defaultInclude
     */
    public boolean getDefaultIncludeStrategy(Class<?> entityClass) {
        if (entityClass.isAnnotationPresent(AuditEntity.class)) {
            AuditEntity auditEntity = entityClass.getAnnotation(AuditEntity.class);
            return auditEntity.defaultInclude();
        }
        return auditProperties.isDefaultInclude();
    }

    /**
     * 将驼峰命名转换为显示名称
     */
    private String convertFieldNameToDisplayName(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        char[] chars = fieldName.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            char currentChar = chars[i];

            if (Character.isUpperCase(currentChar) && i > 0) {
                char prevChar = chars[i - 1];

                if (Character.isLowerCase(prevChar) || 
                    (i + 1 < chars.length && Character.isLowerCase(chars[i + 1]))) {
                    result.append(' ');
                }
            }

            if (i == 0) {
                result.append(Character.toUpperCase(currentChar));
            } else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }

    /**
     * 在类及其父类中查找指定字段
     */
    private Field findFieldInClass(Class<?> clazz, String fieldName) {
        Assert.notNull(clazz, "Class cannot be null");
        Assert.hasText(fieldName, "Field name cannot be empty");
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                Field field = currentClass.getDeclaredField(fieldName);
                ReflectionUtils.makeAccessible(field);
                log.trace("在类 {} 找到字段 {} ", currentClass.getName(), fieldName);
                return field;
            } catch (NoSuchFieldException e) {
                log.debug("在类 {} 中未找到字段 {}，继续查找父类", currentClass.getName(), fieldName);
                currentClass = currentClass.getSuperclass();
            }
        }
        throw new NotFoundException("Field '" + fieldName + "' not found in class " + clazz.getName());
    }
}