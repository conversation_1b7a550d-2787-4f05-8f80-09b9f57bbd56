package com.kerryprops.kip.audit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.util.ClassUtils;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * 审计实体扫描器工具类.
 * 负责扫描带有@AuditEntity注解的实体类。
 *
 * <AUTHOR>
 */
@Slf4j
public class AuditEntityScanner {

    private static final String DEFAULT_RESOURCE_PATTERN = "**/*.class";

    /**
     * 扫描带有@AuditEntity注解的实体类.
     *
     * @param scanPackages 需要扫描的包路径数组
     * @return 审计实体类集合
     */
    public Set<Class<?>> scanAuditEntities(String[] scanPackages) {
        var auditEntityClasses = new HashSet<Class<?>>();
        var resolver = new PathMatchingResourcePatternResolver();
        var metadataReaderFactory = new CachingMetadataReaderFactory();

        for (String basePackage : scanPackages) {
            var packageSearchPath = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX +
                    ClassUtils.convertClassNameToResourcePath(basePackage) + "/" + DEFAULT_RESOURCE_PATTERN;

            try {
                var resources = resolver.getResources(packageSearchPath);

                for (Resource resource : resources) {
                    if (resource.isReadable()) {
                        var metadataReader = metadataReaderFactory.getMetadataReader(resource);
                        var className = metadataReader.getClassMetadata().getClassName();

                        try {
                            var clazz = ClassUtils.forName(className, this.getClass().getClassLoader());

                            // 检查是否带有@AuditEntity注解
                            if (AnnotationUtils.findAnnotation(clazz, AuditEntity.class) != null) {
                                auditEntityClasses.add(clazz);
                            }
                        } catch (ClassNotFoundException | LinkageError e) {
                            // 忽略无法加载的类
                        }
                    }
                }
            } catch (IOException e) {
                log.warn("扫描包 {} 时发生错误: {}", basePackage, e.getMessage());
            }
        }

        return auditEntityClasses;
    }
}