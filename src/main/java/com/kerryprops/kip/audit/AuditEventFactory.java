package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
public class AuditEventFactory {

    private final AuditProperties auditProperties;
    @Nullable
    private final EntityCacheManager entityCacheManager;

    @Nullable
    @Value("${spring.application.name:UNKOWN-SERVICE}")
    private String applicationName;

    /**
     * 创建审计事件请求对象
     */
    public AuditEventRequest createAuditEventRequest(Object entity, AuditOperation operation) {
        var request = new AuditEventRequest();

        request.setEntityType(entity.getClass().getSimpleName());
        request.setEntityId(entityCacheManager != null ? entityCacheManager.findEntityId(entity) : "unknown");
        request.setOperation(operation);

        setUserInfo(request);
        setRequestInfo(request);

        return request;
    }

    /**
     * 设置用户信息
     */
    private void setUserInfo(AuditEventRequest request) {
        Assert.notNull(request, "AuditEventRequest cannot be null");
        request.setUserId(AuditContext.getCurrentUserId());
        request.setUserNickname(AuditContext.getCurrentUserNickname());
    }

    /**
     * 设置请求相关信息
     */
    private void setRequestInfo(AuditEventRequest request) {
        Assert.notNull(request, "AuditEventRequest cannot be null");

        request.setServiceId(auditProperties.getServiceId());

        Optional.of(AuditContext.getAuditRequestInfo())
                .ifPresent(requestInfo -> {
                    request.setIpAddress(requestInfo.getIpAddress());
                    request.setUserAgent(requestInfo.getUserAgent());
                    request.setConversationId(requestInfo.getConversationId());
                    request.setCorrelationId(requestInfo.getCorrelationId());
                    request.setUiModel(requestInfo.getUiModel());

                    request.setFilters(List.of(createAuditFilters(requestInfo)));
                });
    }

    /**
     * 创建审计过滤器列表
     */
    private AuditEventRequest.AuditEventFilter createAuditFilters(AuditRequestInfo requestInfo) {
        Assert.notNull(requestInfo, "AuditRequestInfo cannot be null");
        Assert.notNull(requestInfo.getAuditFilterKey(), "Audit filter key is required");
        Assert.notNull(requestInfo.getAuditFilterValue(), "Audit filter value is required");
        var filter = new AuditEventRequest.AuditEventFilter();
        filter.setFilterKey(requestInfo.getAuditFilterKey());
        filter.setFilterValue(requestInfo.getAuditFilterValue());
        return filter;
    }
}