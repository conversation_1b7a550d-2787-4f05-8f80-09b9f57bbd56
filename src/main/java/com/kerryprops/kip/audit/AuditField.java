package com.kerryprops.kip.audit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 审计字段控制注解.
 * 提供字段级别的审计控制，包括别名设置和包含/排除逻辑。
 *
 * <AUTHOR> Zhang
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditField {

    /**
     * 字段的显示别名.
     * 如果未指定，将使用字段名的驼峰转换作为显示名称。
     *
     * @return 字段别名
     */
    String alias() default "";

    /**
     * 是否包含此字段进行审计.
     * 当设置为false时，字段将不会被审计（除非被exclude强制排除）。
     *
     * @return 是否包含
     */
    boolean include() default true;

    /**
     * 是否排除此字段的审计.
     * 当设置为true时，字段将被强制排除，优先级最高。
     *
     * @return 是否排除
     */
    boolean exclude() default false;
}