package com.kerryprops.kip.audit;

import lombok.Data;
import org.springframework.lang.Nullable;

import java.util.List;

@Data
public class AuditEventRequest {

    /**
     * 发送事件的服务 ID
     */
    private String serviceId;

    /**
     * UI 模型名称
     */
    private String uiModel;

    /**
     * 被审计的实体类型
     */
    private String entityType;

    /**
     * 被审计的实体 ID
     */
    private String entityId;

    /**
     * 操作类型
     */
    private AuditOperation operation;

    /**
     * 执行操作的用户
     */
    private Long userId;

    /**
     * 用户名/显示名称
     */
    private String userNickname;

    /**
     * 附加操作上下文
     */
    @Nullable
    private String context;

    /**
     * 客户端 IP 地址
     */
    private String ipAddress;

    /**
     * 客户端用户代理
     */
    private String userAgent;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 关联ID
     */
    private String correlationId;

    /**
     * 变更的字段
     */
    private List<FieldChange> fieldChanges;

    private List<AuditEventFilter> filters;

    @Data
    public static class FieldChange {

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 字段类型
         */
        private String fieldType;

        /**
         * 旧值
         */
        @Nullable
        private String oldValue;

        /**
         * 新值
         */
        @Nullable
        private String newValue;

        /**
         * 字段显示名称.
         * 表示字段的用户友好名称，用于在界面或日志中显示而非直接使用字段的代码名称。
         */
        private String fieldDisplayName;
    }

    @Data
    public static class AuditEventFilter {

        /**
         * 过滤键名
         */
        private String filterKey;

        /**
         * 过滤键值
         */
        private String filterValue;

    }

}