package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * AuditClient.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-07-01 17:41:32
 **/
@RequiredArgsConstructor
public class AuditClient {

    private final RestTemplate restTemplate;

    private final AuditProperties auditProperties;

    public void send(List<AuditEventRequest> requests) {
        restTemplate.postForEntity(auditProperties.getUrl(), requests, Void.class);
    }

}
