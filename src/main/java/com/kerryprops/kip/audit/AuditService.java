package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

@Slf4j
@RequiredArgsConstructor
public class AuditService {

    @Nullable
    private final EntityCacheManager entityCacheManager;
    private final ChangeDetector changeDetector;
    private final AuditEventFactory auditEventFactory;
    private final AuditQueue auditQueue;

    /**
     * 在实体更新前调用，缓存实体原始状态
     *
     * @param entity 待更新的实体
     */
    public void beforeUpdate(Object entity) {
        if (entityCacheManager != null) {
            entityCacheManager.cacheOriginalEntity(entity);
        } else {
            log.debug("EntityCacheManager不可用，跳过实体原始状态缓存: {}",
                    entity.getClass().getSimpleName());
        }
    }

    public void recordCreation(Object entity) {
        try {
            var auditLog = auditEventFactory.createAuditEventRequest(entity, AuditOperation.CREATE);
            var fieldChanges = changeDetector.detectChanges(null, entity);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity creation: ", e);
        }
    }

    public void recordUpdate(Object entity) {
        try {
            Object oldEntity = fetchOriginalEntityWithFallback(entity);
            var auditLog = auditEventFactory.createAuditEventRequest(entity, AuditOperation.UPDATE);
            var fieldChanges = changeDetector.detectChanges(oldEntity, entity);
            if(fieldChanges.isEmpty()) {
                log.warn("No field changes detected for entity: {}", entity);
                return; // 如果没有字段变化，则不记录审计日志
            }
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity update: ", e);
        }
    }

    /**
     * 获取原始实体状态，实现健壮的降级策略
     * 优先从EntityCacheManager获取，如果不可用则使用降级策略
     *
     * @param entity 当前实体
     * @return 原始实体状态，降级时返回当前实体状态以避免null比较
     */
    private Object fetchOriginalEntityWithFallback(Object entity) {
        // 1. 首先检查EntityCacheManager是否可用
        if (entityCacheManager == null) {
            log.warn("EntityCacheManager不可用 - 可能原因：测试环境、Bean创建顺序问题或配置错误");
            log.info("降级处理：使用当前实体状态作为基准，本次更新将不会产生变更记录");
            return entity; // 返回当前实体，避免null vs entity的比较
        }

        try {
            // 2. 尝试从EntityCacheManager获取原始实体
            Object originalEntity = entityCacheManager.getOriginalEntity(entity);

            // 3. 检查EntityCacheManager是否返回了当前实体（表示缓存未命中的降级处理）
            if (originalEntity == entity) {
                log.debug("EntityCacheManager缓存未命中，已使用降级策略");
            }

            return originalEntity;
        } catch (Exception e) {
            log.error("从EntityCacheManager获取原始实体失败: {}, id: {}, 使用降级策略",
                    entity.getClass().getSimpleName(),
                    entityCacheManager != null ? entityCacheManager.findEntityId(entity) : "unknown", e);
            log.info("降级处理：使用当前实体状态作为基准，本次更新将不会产生变更记录");
            return entity; // 降级策略：返回当前实体
        }
    }

    public void recordDeletion(Object entity) {
        try {
            AuditEventRequest auditLog = auditEventFactory.createAuditEventRequest(entity, AuditOperation.DELETE);
            var fieldChanges = changeDetector.detectChanges(entity, null);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity deletion: ", e);
        }
    }

}