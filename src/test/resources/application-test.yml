spring:
  application:
    name: audit-spring-boot-starter
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
audit:
  max-field-length: 1000
  excluded-fields:
    - password
    - token
    - secret
  header:
    user: x-user
  url: http://localhost:8089/api/audit/events/batch
  service-id: ${spring.application.name}
  cron: "* * * * * ?"
  mybatis:
    enabled: true
    enable-detailed-logging: true
    enable-batch-audit: true
    max-batch-size: 100
    common-entity-keys:
      - entity
      - record
      - param
      - data
      - model
    excluded-mapper-patterns:
      - "*.selectBy*"
      - "*.findBy*"
      - "*.countBy*"
      - "*.existsBy*"
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  type-aliases-package: com.kerryprops.kip.audit.mybatis

logging:
  level:
    org.zalando.logbook: trace
    com.kerryprops.kip.audit.mybatis: debug
    org.apache.ibatis: debug