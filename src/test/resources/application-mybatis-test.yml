spring:
  application:
    name: mybatis-test-app
  datasource:
    url: jdbc:h2:mem:mybatis-testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:
    schema: classpath:mybatis-test-schema.sql
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.H2Dialect
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
  h2:
    console:
      enabled: true

audit:
  enabled: true
  service-id: mybatis-test-service
  url: http://localhost:8089/api/audit/events/batch
  max-field-length: 1000
  excluded-fields:
    - password
    - secret
    - token
  field-aliases: {}
  default-include: true
  jpa:
    enabled: true
  mybatis:
    enabled: true
  header:
    user: x-user
    ui-model: X-UI-Model
    conversation-id: X-Conversation-Id
    correlation-id: X-Correlation-ID
    audit-filter-key: X-Audit-Filter-Key
    audit-filter-value: X-Audit-Filter-Value
  queue:
    capacity: 10000
    batch-size: 100
  http-client:
    connect-timeout: 5000
    read-timeout: 10000

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.kerryprops.kip.audit: DEBUG
    org.springframework.test: DEBUG
