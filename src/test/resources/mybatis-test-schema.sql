-- MyBatis Test Database Schema
-- This schema is specifically for MyBatis integration tests

-- Drop tables if they exist
DROP TABLE IF EXISTS test_products;

-- Create test_products table for MyBatis tests
CREATE TABLE test_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    stock_quantity INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    internal_version INTEGER DEFAULT 1
);

-- Insert some test data
INSERT INTO test_products (name, price, category, description, stock_quantity, is_active) VALUES
('Test Product 1', 99.99, 'Electronics', 'A test product for MyBatis testing', 10, true),
('Test Product 2', 149.99, 'Books', 'Another test product', 5, true),
('Test Product 3', 29.99, 'Clothing', 'Third test product', 20, false);
