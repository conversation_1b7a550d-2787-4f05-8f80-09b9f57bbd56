package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.AuditEntity;
import com.kerryprops.kip.audit.AuditField;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 测试用的实体类，使用@AuditEntity注解实现审计功能
 * 展示了@AuditEntity和@AuditField注解的使用方式
 */
@Data
@Entity
@Table(name = "test_users")
@AuditEntity(
        excludeFields = {"internalVersion"},
        defaultInclude = true,
        description = "测试用户实体"
)
public class TestUser {

    @AuditField(alias = "用户ID")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @AuditField(alias = "用户名")
    @Column(nullable = false, unique = true)
    private String username;

    @AuditField(alias = "邮箱地址")
    @Column(nullable = false)
    private String email;

    @AuditField(alias = "年龄")
    private Integer age;

    @AuditField(alias = "部门")
    private String department;

    @AuditField(alias = "创建时间")
    private LocalDateTime createdAt;

    @AuditField(alias = "更新时间")
    private LocalDateTime updatedAt;

    @AuditField(alias = "状态")
    private String status;

    // 被 @AuditEntity.excludeFields 排除的字段
    private String internalVersion;

    // 被字段级别强制排除的敏感字段
    @AuditField(exclude = true)
    private String sensitiveData;

    // 字段级别选择不包含的字段
    @AuditField(include = false, alias = "内部备注")
    private String internalNotes;

}
