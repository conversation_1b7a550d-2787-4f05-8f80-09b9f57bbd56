package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.AuditAutoConfiguration;
import com.kerryprops.kip.audit.AuditEntityScanner;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试实体扫描和注册功能
 */
@SpringBootTest(classes = EntityScanConfigurationTest.TestApplication.class)
@TestPropertySource(properties = {
        "audit.enabled=true",
        "logging.level.com.kerryprops.kip.audit=DEBUG",
        "spring.application.name=test-app"
})
@DisplayName("实体扫描和注册功能测试")
class EntityScanConfigurationTest {

    @SpringBootApplication(scanBasePackages = "com.kerryprops.kip.audit")
    @EntityScan(basePackages = "com.kerryprops.kip.audit.integration")
    @MapperScan(basePackages = "com.kerryprops.kip.audit.mybatis")
    @Import(AuditAutoConfiguration.class)
    static class TestApplication {
    }


    @Test
    @DisplayName("应该正确扫描带有@AuditEntity注解的实体类")
    void shouldScanAuditEntityAnnotatedClasses(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证实体扫描器被正确创建和使用
        var auditEntityScanner = applicationContext.getBean(AuditEntityScanner.class);
        assertThat(auditEntityScanner).isNotNull();

        // 通过扫描包来验证TestUser实体被发现
        var auditEntities = auditEntityScanner.scanAuditEntities(new String[]{"com.kerryprops.kip.audit.integration"});
        assertThat(auditEntities)
                .isNotEmpty()
                .anyMatch(clazz -> clazz.equals(TestUser.class));
    }

    @Test
    @DisplayName("应该正确处理已有EntityListeners注解的实体")
    void shouldHandleEntitiesWithExistingEntityListeners(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证配置类能够正确初始化，即使实体已有EntityListeners注解
        // 这个测试主要验证不会抛出异常
    }
}
