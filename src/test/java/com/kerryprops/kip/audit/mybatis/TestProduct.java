package com.kerryprops.kip.audit.mybatis;

import com.kerryprops.kip.audit.AuditEntity;
import com.kerryprops.kip.audit.AuditField;
import jakarta.persistence.Id;
import lombok.Data;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 测试用的产品实体类，用于演示MyBatis审计功能
 * 
 * <AUTHOR>
 */
@Data
@AuditEntity(
    excludeFields = {"internalVersion"},
    defaultInclude = true,
    description = "测试产品实体"
)
public class TestProduct {

    /**
     * 产品ID
     */
    @Id
    @Nullable
    private Long id;

    /**
     * 产品名称
     */
    @AuditField(alias = "产品名称", include = true)
    @Nullable
    private String name;

    /**
     * 产品描述
     */
    @AuditField(alias = "产品描述")
    @Nullable
    private String description;

    /**
     * 产品价格
     */
    @AuditField(alias = "产品价格")
    @Nullable
    private BigDecimal price;

    /**
     * 产品状态
     */
    @AuditField(alias = "产品状态")
    @Nullable
    private String status;

    /**
     * 创建时间
     */
    @Nullable
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Nullable
    private LocalDateTime updatedAt;

    /**
     * 内部版本号（排除审计）
     */
    @Nullable
    private Integer internalVersion;

    /**
     * 创建者ID
     */
    @AuditField(alias = "创建者")
    @Nullable
    private Long createdBy;

    /**
     * 更新者ID
     */
    @AuditField(alias = "更新者")
    @Nullable
    private Long updatedBy;
}
